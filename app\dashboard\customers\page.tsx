"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Users, Plus, Search, Edit, Trash2, Building2, Star, Phone, Mail, MapPin } from "lucide-react"
import { useOptimizedData } from "@/hooks/use-optimized-data"
import { useNextAuthData } from "@/hooks/use-nextauth-data"
import { useAuth } from "@/components/auth-provider"
import { useRole } from "@/hooks/use-role"
import { useToast } from "@/hooks/use-toast"
import { EnhancedCustomerFormV2 } from "@/components/customers/enhanced-customer-form-v2"

// DEBUG: Log what component we're importing
// Debug: Component imported successfully
import { CustomerFormData, CustomerDatabaseSchema, mapFormToDatabase, mapDatabaseToForm } from "@/app/types/customer"
import { PageHeader } from "@/components/enhanced-breadcrumb"
import { EnhancedCustomerTable } from "@/components/customers/enhanced-customer-table"
import { AdvancedSearch } from "@/components/customers/advanced-search"
import { CustomerDetailView } from "@/components/customers/customer-detail-view"

// Use the enhanced customer interface from types
type Customer = CustomerDatabaseSchema

export default function CustomersPage() {
  const { user } = useAuth()
  const { isAdmin } = useRole()
  const { toast } = useToast()
  
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [advancedFilteredCustomers, setAdvancedFilteredCustomers] = useState<Customer[]>([])

  // ✅ FIXED: Use NextAuth-compatible data hook that works with API endpoints
  const { data: customers = [], loading, create, update, remove } = useNextAuthData<Customer>({
    table: "customers",
    enableCache: true,
    cacheTTL: 2 * 60 * 1000, // 2-minute cache
  })

  // Use advanced filtered customers if available, otherwise use all customers
  const displayCustomers = advancedFilteredCustomers.length > 0 ? advancedFilteredCustomers : customers

  // Memoize statistics based on displayed customers
  const stats = useMemo(() => {
    const dataToUse = displayCustomers
    return {
      total: dataToUse.length,
      active: dataToUse.filter(c => c.status === 'Active').length,
      premium: dataToUse.filter(c => c.customer_tier === 'Gold' || c.customer_tier === 'Platinum' || c.customer_tier === 'VIP').length,
      companies: new Set(dataToUse.map(c => c.company).filter(Boolean)).size,
    }
  }, [displayCustomers])

  const handleAddCustomer = async (formData: CustomerFormData) => {
    if (!user) return

    try {
      const customerData = mapFormToDatabase(formData, user.id)
      const result = await create(customerData, "customers")

      if (result) {
        setIsAddDialogOpen(false)
        toast({
          title: "Success",
          description: "Customer added successfully",
        })
      }
    } catch (error) {
      console.error("Error adding customer:", error)
      toast({
        title: "Error",
        description: "Failed to add customer. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleEditCustomer = async (formData: CustomerFormData) => {
    if (!selectedCustomer || !user) return

    try {
      const customerData = mapFormToDatabase(formData, user.id)
      const result = await update(selectedCustomer.id, customerData, "customers")

      if (result) {
        setIsEditDialogOpen(false)
        setSelectedCustomer(null)
        toast({
          title: "Success",
          description: "Customer updated successfully",
        })
      }
    } catch (error) {
      console.error("Error updating customer:", error)
      toast({
        title: "Error",
        description: "Failed to update customer. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteCustomer = async (customer: Customer) => {
    if (confirm(`Are you sure you want to delete ${customer.name}?`)) {
      const result = await remove(customer.id, "customers")
      if (result) {
        toast({
          title: "Success",
          description: "Customer deleted successfully",
        })
      }
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <PageHeader
        title="Customers"
        description={
          isAdmin()
            ? "Manage all customer relationships across the system."
            : "Manage your customer relationships and contact information."
        }
      >
        {isAdmin() && (
          <Badge variant="info" className="text-xs">
            Admin View
          </Badge>
        )}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Customer
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Customer</DialogTitle>
              <DialogDescription>
                Create a new customer record in your CRM system.
              </DialogDescription>
            </DialogHeader>
            {(() => {
              console.log("🚀 [DEBUG] About to render EnhancedCustomerFormV2 component")
              console.log("🚀 [DEBUG] Component reference:", EnhancedCustomerFormV2)
              console.log("🚀 [DEBUG] Component type:", typeof EnhancedCustomerFormV2)
              return (
                <EnhancedCustomerFormV2
                  onSubmit={handleAddCustomer}
                  onCancel={() => setIsAddDialogOpen(false)}
                />
              )
            })()}
          </DialogContent>
        </Dialog>
      </PageHeader>

      {/* Advanced Search & Filters */}
      <AdvancedSearch
        customers={customers}
        onFiltersChange={setAdvancedFilteredCustomers}
      />

      {/* Statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card variant="elevated" className="hover-lift transition-all duration-normal">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="ui-label text-muted-foreground">Total Customers</CardTitle>
            <div className="p-2 bg-primary/10 rounded-lg">
              <Users className="h-4 w-4 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.total}</div>
            <p className="ui-caption mt-1">All registered customers</p>
          </CardContent>
        </Card>
        <Card variant="elevated" className="hover-lift transition-all duration-normal">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="ui-label text-muted-foreground">Active</CardTitle>
            <div className="p-2 bg-success/10 rounded-lg">
              <div className="h-4 w-4 bg-success rounded-full" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.active}</div>
            <p className="ui-caption mt-1">Currently active</p>
          </CardContent>
        </Card>
        <Card variant="elevated" className="hover-lift transition-all duration-normal">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="ui-label text-muted-foreground">Premium</CardTitle>
            <div className="p-2 bg-warning/10 rounded-lg">
              <Star className="h-4 w-4 text-warning" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.premium}</div>
            <p className="ui-caption mt-1">Premium tier customers</p>
          </CardContent>
        </Card>
        <Card variant="elevated" className="hover-lift transition-all duration-normal">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="ui-label text-muted-foreground">Companies</CardTitle>
            <div className="p-2 bg-info/10 rounded-lg">
              <Building2 className="h-4 w-4 text-info" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{stats.companies}</div>
            <p className="ui-caption mt-1">Unique companies</p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Customer Table */}
      <EnhancedCustomerTable
        customers={displayCustomers}
        loading={loading}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        onView={(customer) => {
          setSelectedCustomer(customer)
          setIsDetailViewOpen(true)
        }}
        onEdit={(customer) => {
          setSelectedCustomer(customer)
          setIsEditDialogOpen(true)
        }}
        onDelete={handleDeleteCustomer}
      />

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Customer</DialogTitle>
            <DialogDescription>
              Update customer information in your CRM system.
            </DialogDescription>
          </DialogHeader>
          {selectedCustomer && (
            <EnhancedCustomerFormV2
              initialData={mapDatabaseToForm(selectedCustomer)}
              onSubmit={handleEditCustomer}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setSelectedCustomer(null)
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Customer Detail View Dialog */}
      <Dialog open={isDetailViewOpen} onOpenChange={setIsDetailViewOpen}>
        <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto">
          {selectedCustomer && (
            <CustomerDetailView
              customer={selectedCustomer}
              onEdit={() => {
                setIsDetailViewOpen(false)
                setIsEditDialogOpen(true)
              }}
              onDelete={() => {
                setIsDetailViewOpen(false)
                handleDeleteCustomer(selectedCustomer)
              }}
              onClose={() => {
                setIsDetailViewOpen(false)
                setSelectedCustomer(null)
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Helper function to get customer tier badge color
function getTierBadgeColor(tier?: string) {
  switch (tier) {
    case 'Bronze': return 'bg-amber-100 text-amber-800'
    case 'Silver': return 'bg-gray-100 text-gray-800'
    case 'Gold': return 'bg-yellow-100 text-yellow-800'
    case 'Platinum': return 'bg-purple-100 text-purple-800'
    case 'VIP': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}