import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { createClient } from '@supabase/supabase-js'

// Create server-side Supabase client with service role for dashboard stats
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(request: NextRequest) {
  try {
    // Get NextAuth session
    const session = await getServerSession()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = (session.user as any)?.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    console.log('🔍 [DASHBOARD-STATS-API] Fetching stats for user:', userId)

    // Use service role to query dashboard stats with proper user context
    // We'll query each table directly since the views use auth.uid() which won't work here
    const [customersRes, leadsRes, opportunitiesRes, companiesRes, tasksRes] = await Promise.all([
      supabaseAdmin.from('customers').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      supabaseAdmin.from('leads').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      supabaseAdmin.from('deals').select('id', { count: 'exact', head: true }).eq('user_id', userId), // Use deals table directly
      supabaseAdmin.from('companies').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      supabaseAdmin.from('tasks').select('id', { count: 'exact', head: true }).eq('user_id', userId)
    ])

    // Check for errors
    if (customersRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Customers query error:', customersRes.error)
      throw customersRes.error
    }
    if (leadsRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Leads query error:', leadsRes.error)
      throw leadsRes.error
    }
    if (opportunitiesRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Opportunities query error:', opportunitiesRes.error)
      throw opportunitiesRes.error
    }
    if (companiesRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Companies query error:', companiesRes.error)
      throw companiesRes.error
    }
    if (tasksRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Tasks query error:', tasksRes.error)
      throw tasksRes.error
    }

    const stats = {
      customers: customersRes.count || 0,
      leads: leadsRes.count || 0,
      opportunities: opportunitiesRes.count || 0,
      companies: companiesRes.count || 0,
      tasks: tasksRes.count || 0
    }

    console.log('✅ [DASHBOARD-STATS-API] Stats fetched successfully:', stats)

    return NextResponse.json(stats)

  } catch (error: any) {
    console.error('❌ [DASHBOARD-STATS-API] Error fetching dashboard stats:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch dashboard stats',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
