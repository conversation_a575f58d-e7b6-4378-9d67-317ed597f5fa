"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useSession } from "next-auth/react"
import { useLanguage } from "@/components/language-provider"
import { supabase } from "@/lib/supabase"
import {
  Users, TrendingUp, DollarSign, Target, CheckCircle, Clock, Building2
} from "lucide-react"

export default function Dashboard() {
  const { data: session } = useSession()
  const user = session?.user
  const userId = (user as any)?.id
  const { t } = useLanguage()
  const [stats, setStats] = useState({
    customers: 0,
    leads: 0,
    opportunities: 0,
    companies: 0,
    tasks: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!user || !userId) return

    const fetchStats = async () => {
      try {
        const [customersRes, leadsRes, opportunitiesRes, companiesRes, tasksRes] = await Promise.all([
          supabase.from('customers').select('id', { count: 'exact', head: true }).eq('user_id', userId),
          supabase.from('leads').select('id', { count: 'exact', head: true }).eq('user_id', userId),
          supabase.from('opportunities').select('id', { count: 'exact', head: true }).eq('user_id', userId),
          supabase.from('companies').select('id', { count: 'exact', head: true }).eq('user_id', userId),
          supabase.from('tasks').select('id', { count: 'exact', head: true }).eq('user_id', userId)
        ])

        setStats({
          customers: customersRes.count || 0,
          leads: leadsRes.count || 0,
          opportunities: opportunitiesRes.count || 0,
          companies: companiesRes.count || 0,
          tasks: tasksRes.count || 0
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [user, userId])

  if (!user) {
    return <div className="p-6">Loading...</div>
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">Welcome back, {user.email}!</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? "..." : stats.customers}</div>
            <Badge variant="info" className="mt-2">Active</Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Leads</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? "..." : stats.leads}</div>
            <Badge variant="warning" className="mt-2">Pending</Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Opportunities</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? "..." : stats.opportunities}</div>
            <Badge variant="default" className="mt-2">Open</Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Companies</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? "..." : stats.companies}</div>
            <Badge variant="success" className="mt-2">Active</Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tasks</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loading ? "..." : stats.tasks}</div>
            <Badge variant="default" className="mt-2">Pending</Badge>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">No recent activity to display.</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p className="text-sm text-muted-foreground">Get started with your CRM:</p>
            <ul className="text-sm space-y-1">
              <li>• Add your first customer</li>
              <li>• Create a new lead</li>
              <li>• Set up your first opportunity</li>
              <li>• Organize your tasks</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
