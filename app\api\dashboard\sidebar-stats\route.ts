import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { createClient } from '@supabase/supabase-js'

// Create server-side Supabase client with service role for sidebar stats
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(request: NextRequest) {
  try {
    // Get NextAuth session
    const session = await getServerSession()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = (session.user as any)?.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    console.log('🔍 [SIDEBAR-STATS-API] Fetching sidebar stats for user:', userId)

    // Query the three main stats for sidebar (matching dashboard_stats_batch view)
    const [customersRes, leadsRes, opportunitiesRes] = await Promise.all([
      supabaseAdmin.from('customers').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      supabaseAdmin.from('leads').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      supabaseAdmin.from('deals').select('id', { count: 'exact', head: true }).eq('user_id', userId), // Use deals table directly
    ])

    // Check for errors
    if (customersRes.error) {
      console.error('❌ [SIDEBAR-STATS-API] Customers query error:', customersRes.error)
      throw customersRes.error
    }
    if (leadsRes.error) {
      console.error('❌ [SIDEBAR-STATS-API] Leads query error:', leadsRes.error)
      throw leadsRes.error
    }
    if (opportunitiesRes.error) {
      console.error('❌ [SIDEBAR-STATS-API] Opportunities query error:', opportunitiesRes.error)
      throw opportunitiesRes.error
    }

    const stats = [{
      customers_count: customersRes.count || 0,
      leads_count: leadsRes.count || 0,
      opportunities_count: opportunitiesRes.count || 0
    }]

    console.log('✅ [SIDEBAR-STATS-API] Sidebar stats fetched successfully:', stats[0])

    return NextResponse.json(stats)

  } catch (error: any) {
    console.error('❌ [SIDEBAR-STATS-API] Error fetching sidebar stats:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch sidebar stats',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
