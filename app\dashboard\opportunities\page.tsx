"use client"

import React, { useState, useEffect, use<PERSON>allback } from "react"
import { Target, Edit, Trash2, MoreHorizontal, Plus, Search, Filter, DollarSign, Calendar, Building2, ArrowUpDown, ArrowUp, ArrowDown, Download } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"

import { useOptimizedData } from "@/hooks/use-optimized-data"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/components/auth-provider"
import { useLanguage } from "@/components/language-provider"

interface Opportunity {
  id: string
  title: string
  company: string
  contact_person?: string
  phone?: string
  value: number
  stage: "prospecting" | "qualification" | "proposal" | "negotiation" | "closed_won" | "closed_lost"
  probability: number
  expected_close_date?: string
  source?: string
  description?: string
  notes?: string
  created_at: string
  user_id: string
}

type OpportunityStage = "prospecting" | "qualification" | "proposal" | "negotiation" | "closed_won" | "closed_lost"

const OpportunityForm = React.memo(({
  opportunity,
  onSubmit,
  onCancel,
  isEdit = false
}: {
  opportunity?: Opportunity
  onSubmit: (data: Partial<Opportunity>) => void
  onCancel: () => void
  isEdit?: boolean
}) => {
  const { t } = useLanguage()
  const [formData, setFormData] = useState({
    title: opportunity?.title || "",
    company: opportunity?.company || "",
    contact_person: opportunity?.contact_person || "",
    phone: opportunity?.phone || "",
    value: opportunity?.value?.toString() || "",
    stage: opportunity?.stage || "prospecting" as OpportunityStage,
    probability: opportunity?.probability?.toString() || "10",
    expected_close_date: opportunity?.expected_close_date || "",
    source: opportunity?.source || "",
    description: opportunity?.description || "",
    notes: opportunity?.notes || "",
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Required field validation
    if (!formData.title.trim()) {
      newErrors.title = "Opportunity name is required"
    }

    if (!formData.company.trim()) {
      newErrors.company = "Company name is required"
    }

    if (!formData.value || parseFloat(formData.value) <= 0) {
      newErrors.value = "Deal value must be greater than 0"
    }

    // Probability validation
    const probability = parseInt(formData.probability)
    if (isNaN(probability) || probability < 0 || probability > 100) {
      newErrors.probability = "Probability must be between 0 and 100"
    }

    // Date validation
    if (formData.expected_close_date) {
      const closeDate = new Date(formData.expected_close_date)
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      if (closeDate < today) {
        newErrors.expected_close_date = "Close date cannot be in the past"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setErrors({})

    try {
      const submitData = {
        ...formData,
        value: parseFloat(formData.value) || 0,
        probability: parseInt(formData.probability) || 10,
        expected_close_date: formData.expected_close_date || undefined
      }
      await onSubmit(submitData)
    } catch (error) {
      setErrors({ submit: "Failed to save opportunity. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Auto-update probability based on stage
  const handleStageChange = (stage: OpportunityStage) => {
    const stageProbabilities: Record<OpportunityStage, string> = {
      "prospecting": "10",
      "qualification": "25",
      "proposal": "50",
      "negotiation": "75",
      "closed_won": "100",
      "closed_lost": "0"
    }
    setFormData(prev => ({
      ...prev,
      stage,
      probability: stageProbabilities[stage] || prev.probability
    }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="title" className={errors.title ? "text-red-600" : ""}>
            Opportunity Name *
          </Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => handleChange("title", e.target.value)}
            required
            placeholder="e.g., Website Redesign Project"
            className={errors.title ? "border-red-500 focus:border-red-500" : ""}
          />
          {errors.title && (
            <p className="text-sm text-red-600 mt-1">{errors.title}</p>
          )}
        </div>
        <div>
          <Label htmlFor="company" className={errors.company ? "text-red-600" : ""}>
            Company *
          </Label>
          <Input
            id="company"
            value={formData.company}
            onChange={(e) => handleChange("company", e.target.value)}
            required
            className={errors.company ? "border-red-500 focus:border-red-500" : ""}
          />
          {errors.company && (
            <p className="text-sm text-red-600 mt-1">{errors.company}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="contact_person">Contact Person</Label>
          <Input
            id="contact_person"
            value={formData.contact_person}
            onChange={(e) => handleChange("contact_person", e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            value={formData.phone}
            onChange={(e) => handleChange("phone", e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <div>
          <Label htmlFor="source">Source</Label>
          <Input
            id="source"
            value={formData.source}
            onChange={(e) => handleChange("source", e.target.value)}
            placeholder="e.g., Website, Referral, Cold Call"
          />
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="value" className={errors.value ? "text-red-600" : ""}>
            Deal Value ($) *
          </Label>
          <Input
            id="value"
            type="number"
            step="0.01"
            min="0"
            value={formData.value}
            onChange={(e) => handleChange("value", e.target.value)}
            required
            placeholder="0.00"
            className={errors.value ? "border-red-500 focus:border-red-500" : ""}
          />
          {errors.value && (
            <p className="text-sm text-red-600 mt-1">{errors.value}</p>
          )}
        </div>
        <div>
          <Label htmlFor="stage">Stage</Label>
          <Select value={formData.stage} onValueChange={handleStageChange}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="prospecting">Prospecting</SelectItem>
              <SelectItem value="qualification">Qualification</SelectItem>
              <SelectItem value="proposal">Proposal</SelectItem>
              <SelectItem value="negotiation">Negotiation</SelectItem>
              <SelectItem value="closed_won">Closed Won</SelectItem>
              <SelectItem value="closed_lost">Closed Lost</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="probability" className={errors.probability ? "text-red-600" : ""}>
            Probability (%)
          </Label>
          <Input
            id="probability"
            type="number"
            min="0"
            max="100"
            value={formData.probability}
            onChange={(e) => handleChange("probability", e.target.value)}
            className={errors.probability ? "border-red-500 focus:border-red-500" : ""}
          />
          {errors.probability && (
            <p className="text-sm text-red-600 mt-1">{errors.probability}</p>
          )}
        </div>
      </div>

      <div>
        <Label htmlFor="expected_close_date" className={errors.expected_close_date ? "text-red-600" : ""}>
          Expected Close Date
        </Label>
        <Input
          id="expected_close_date"
          type="date"
          value={formData.expected_close_date}
          onChange={(e) => handleChange("expected_close_date", e.target.value)}
          className={errors.expected_close_date ? "border-red-500 focus:border-red-500" : ""}
        />
        {errors.expected_close_date && (
          <p className="text-sm text-red-600 mt-1">{errors.expected_close_date}</p>
        )}
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleChange("description", e.target.value)}
          rows={2}
          placeholder="Brief description of the opportunity..."
        />
      </div>

      <div>
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          value={formData.notes}
          onChange={(e) => handleChange("notes", e.target.value)}
          rows={3}
          placeholder="Additional notes and follow-up information..."
        />
      </div>

      {errors.submit && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {isEdit ? "Updating..." : "Creating..."}
            </>
          ) : (
            `${isEdit ? "Update" : "Create"} Opportunity`
          )}
        </Button>
      </div>
    </form>
  )
})

export default function OpportunitiesPage() {
  const { t } = useLanguage()
  const { toast } = useToast()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedOpportunity, setSelectedOpportunity] = useState<Opportunity | null>(null)
  const [deletingOpportunityId, setDeletingOpportunityId] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [stageFilter, setStageFilter] = useState<string>("all")
  const [sortField, setSortField] = useState<keyof Opportunity | "">("")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [selectedOpportunities, setSelectedOpportunities] = useState<Set<string>>(new Set())
  const [showBulkActions, setShowBulkActions] = useState(false)

  // ✅ PERFORMANCE OPTIMIZED: Selective fields and caching for better performance
  const { data: opportunities = [], loading, create, update, remove } = useOptimizedData<Opportunity>({
    table: "opportunities",
    select: "id, title, company, contact_person, value, stage, probability, expected_close_date", // ✅ Reduced fields
    requiresAuth: true,
    realtime: false, // ✅ Disabled for faster loading
    enableCache: true,
    cacheTTL: 2 * 60 * 1000, // 2-minute cache
  })

  // Debug: Log opportunities data to understand title field issue
  React.useEffect(() => {
    if (opportunities.length > 0) {
      console.log('🔍 [OPPORTUNITIES-DEBUG] First opportunity data:', opportunities[0])
      console.log('🔍 [OPPORTUNITIES-DEBUG] Title field:', opportunities[0]?.title)
      console.log('🔍 [OPPORTUNITIES-DEBUG] All opportunities:', opportunities.map(o => ({ id: o.id, title: o.title, company: o.company })))
    }
  }, [opportunities])

  const filteredOpportunities = (opportunities || [])
    .filter(opportunity => {
      const matchesSearch = opportunity.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           opportunity.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           opportunity.contact_person?.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStage = stageFilter === "all" || opportunity.stage === stageFilter
      return matchesSearch && matchesStage
    })
    .sort((a, b) => {
      if (!sortField) return 0

      let aValue = a[sortField]
      let bValue = b[sortField]

      // Handle different data types
      if (sortField === "value") {
        aValue = Number(aValue) || 0
        bValue = Number(bValue) || 0
      } else if (sortField === "probability") {
        aValue = Number(aValue) || 0
        bValue = Number(bValue) || 0
      } else if (sortField === "expected_close_date") {
        aValue = aValue ? new Date(aValue as string).getTime() : 0
        bValue = bValue ? new Date(bValue as string).getTime() : 0
      } else {
        aValue = String(aValue || "").toLowerCase()
        bValue = String(bValue || "").toLowerCase()
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
      return 0
    })

  const handleSort = (field: keyof Opportunity) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  const SortableHeader = ({ field, children }: { field: keyof Opportunity, children: React.ReactNode }) => {
    const isActive = sortField === field
    const Icon = isActive
      ? (sortDirection === "asc" ? ArrowUp : ArrowDown)
      : ArrowUpDown

    return (
      <TableHead
        className="cursor-pointer hover:bg-gray-50 select-none"
        onClick={() => handleSort(field)}
      >
        <div className="flex items-center space-x-1">
          <span>{children}</span>
          <Icon className={`h-4 w-4 ${isActive ? 'text-primary' : 'text-gray-400'}`} />
        </div>
      </TableHead>
    )
  }

  const handleSelectOpportunity = (opportunityId: string) => {
    const newSelected = new Set(selectedOpportunities)
    if (newSelected.has(opportunityId)) {
      newSelected.delete(opportunityId)
    } else {
      newSelected.add(opportunityId)
    }
    setSelectedOpportunities(newSelected)
    setShowBulkActions(newSelected.size > 0)
  }

  const handleSelectAll = () => {
    if (selectedOpportunities.size === filteredOpportunities.length) {
      setSelectedOpportunities(new Set())
      setShowBulkActions(false)
    } else {
      const allIds = new Set(filteredOpportunities.map(o => o.id))
      setSelectedOpportunities(allIds)
      setShowBulkActions(true)
    }
  }

  const handleBulkDelete = async () => {
    if (selectedOpportunities.size === 0) return

    const confirmDelete = window.confirm(
      `Are you sure you want to delete ${selectedOpportunities.size} opportunities? This action cannot be undone.`
    )

    if (!confirmDelete) return

    try {
      const deletePromises = Array.from(selectedOpportunities).map(id =>
        remove(id, "opportunities")
      )

      await Promise.all(deletePromises)

      toast({
        title: "Success",
        description: `${selectedOpportunities.size} opportunities deleted successfully`,
      })

      setSelectedOpportunities(new Set())
      setShowBulkActions(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete some opportunities",
        variant: "destructive",
      })
    }
  }

  const handleBulkStageUpdate = async (newStage: OpportunityStage) => {
    if (selectedOpportunities.size === 0) return

    try {
      const updatePromises = Array.from(selectedOpportunities).map(id =>
        update(id, { stage: newStage }, "opportunities")
      )

      await Promise.all(updatePromises)

      toast({
        title: "Success",
        description: `${selectedOpportunities.size} opportunities updated successfully`,
      })

      setSelectedOpportunities(new Set())
      setShowBulkActions(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update some opportunities",
        variant: "destructive",
      })
    }
  }

  const handleExportCSV = () => {
    const headers = [
      "Opportunity Name",
      "Company",
      "Contact Person",
      "Phone",
      "Value",
      "Stage",
      "Probability (%)",
      "Expected Close Date",
      "Source",
      "Description",
      "Notes",
      "Created Date"
    ]

    const csvData = filteredOpportunities.map(opportunity => [
      opportunity.title || "",
      opportunity.company || "",
      opportunity.contact_person || "",
      opportunity.phone || "",
      opportunity.value || 0,
      opportunity.stage || "",
      opportunity.probability || 0,
      opportunity.expected_close_date || "",
      opportunity.source || "",
      opportunity.description || "",
      opportunity.notes || "",
      opportunity.created_at ? new Date(opportunity.created_at).toLocaleDateString() : ""
    ])

    const csvContent = [
      headers.join(","),
      ...csvData.map(row =>
        row.map(cell =>
          typeof cell === "string" && cell.includes(",")
            ? `"${cell.replace(/"/g, '""')}"`
            : cell
        ).join(",")
      )
    ].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `opportunities-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "Success",
      description: `Exported ${filteredOpportunities.length} opportunities to CSV`,
    })
  }

  const handleCreateOpportunity = async (data: Partial<Opportunity>) => {
    try {
      const result = await create(data, "opportunities")
      if (result) {
        toast({
          title: "Success",
          description: "Opportunity created successfully",
        })
        setIsDialogOpen(false)
      } else {
        throw new Error("Failed to create opportunity")
      }
    } catch (error: any) {
      console.error("Create opportunity error:", error)
      const errorMessage = error?.message?.includes("email")
        ? "Database schema error: Please contact support"
        : error?.message || "Failed to create opportunity"

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
      throw error // Re-throw for form handling
    }
  }

  const handleUpdateOpportunity = async (data: Partial<Opportunity>) => {
    if (!selectedOpportunity) return

    try {
      const result = await update(selectedOpportunity.id, data, "opportunities")
      if (result) {
        toast({
          title: "Success",
          description: "Opportunity updated successfully",
        })
        setIsEditDialogOpen(false)
        setSelectedOpportunity(null)
      } else {
        throw new Error("Failed to update opportunity")
      }
    } catch (error: any) {
      console.error("Update opportunity error:", error)
      const errorMessage = error?.message || "Failed to update opportunity"

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
      throw error // Re-throw for form handling
    }
  }

  const handleDeleteOpportunity = async () => {
    if (!deletingOpportunityId) return

    try {
      const result = await remove(deletingOpportunityId, "opportunities")
      if (result) {
        toast({
          title: "Success",
          description: "Opportunity deleted successfully",
        })
        setIsDeleteDialogOpen(false)
        setDeletingOpportunityId(null)
      } else {
        throw new Error("Failed to delete opportunity")
      }
    } catch (error: any) {
      console.error("Delete opportunity error:", error)
      const errorMessage = error?.message || "Failed to delete opportunity"

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  const getStageBadge = (stage: string) => {
    const variants = {
      "prospecting": "bg-gray-100 text-gray-800",
      "qualification": "bg-blue-100 text-blue-800",
      "proposal": "bg-yellow-100 text-yellow-800",
      "negotiation": "bg-orange-100 text-orange-800",
      "closed_won": "bg-green-100 text-green-800",
      "closed_lost": "bg-red-100 text-red-800"
    }

    const stageLabels = {
      "prospecting": "Prospecting",
      "qualification": "Qualification",
      "proposal": "Proposal",
      "negotiation": "Negotiation",
      "closed_won": "Closed Won",
      "closed_lost": "Closed Lost"
    }

    return (
      <Badge className={variants[stage as keyof typeof variants] || "bg-gray-100 text-gray-800"}>
        {stageLabels[stage as keyof typeof stageLabels] || stage}
      </Badge>
    )
  }

  const getProbabilityColor = (probability: number) => {
    if (probability >= 75) return "text-green-600"
    if (probability >= 50) return "text-yellow-600"
    if (probability >= 25) return "text-orange-600"
    return "text-gray-600"
  }

  // Calculate stats
  const stats = {
    total: opportunities.length,
    totalValue: opportunities.reduce((sum, o) => sum + (o.value || 0), 0),
    weightedValue: opportunities.reduce((sum, o) => sum + ((o.value || 0) * (o.probability || 0) / 100), 0),
    won: opportunities.filter(o => o.stage === "closed_won").length,
    wonValue: opportunities.filter(o => o.stage === "closed_won").reduce((sum, o) => sum + (o.value || 0), 0),
    active: opportunities.filter(o => !["closed_won", "closed_lost"].includes(o.stage)).length
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold">{t("Opportunities")}</h2>
          <p className="text-gray-500 mt-2">
            Track and manage your sales opportunities and deals.
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Opportunity
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Add New Opportunity</DialogTitle>
              <DialogDescription>
                Create a new sales opportunity to track through your pipeline.
              </DialogDescription>
            </DialogHeader>
            <OpportunityForm
              onSubmit={handleCreateOpportunity}
              onCancel={() => setIsDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-sm text-gray-500">Total Opportunities</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.active}</div>
            <div className="text-sm text-gray-500">Active</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.won}</div>
            <div className="text-sm text-gray-500">Won</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">${stats.totalValue.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Total Value</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">${stats.weightedValue.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Weighted Value</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">${stats.wonValue.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Won Value</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Sales Pipeline</CardTitle>
              <CardDescription>
                {filteredOpportunities.length} opportunities found
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search opportunities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-64"
                />
              </div>
              <Select value={stageFilter} onValueChange={setStageFilter}>
                <SelectTrigger className="w-40">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Stages</SelectItem>
                  <SelectItem value="prospecting">Prospecting</SelectItem>
                  <SelectItem value="qualification">Qualification</SelectItem>
                  <SelectItem value="proposal">Proposal</SelectItem>
                  <SelectItem value="negotiation">Negotiation</SelectItem>
                  <SelectItem value="closed_won">Closed Won</SelectItem>
                  <SelectItem value="closed_lost">Closed Lost</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                onClick={handleExportCSV}
                disabled={filteredOpportunities.length === 0}
              >
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Bulk Actions Bar */}
        {showBulkActions && (
          <div className="px-6 py-3 bg-blue-50 border-b border-blue-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-blue-900">
                  {selectedOpportunities.size} opportunities selected
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Select onValueChange={(value) => handleBulkStageUpdate(value as OpportunityStage)}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Update Stage" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="prospecting">Prospecting</SelectItem>
                    <SelectItem value="qualification">Qualification</SelectItem>
                    <SelectItem value="proposal">Proposal</SelectItem>
                    <SelectItem value="negotiation">Negotiation</SelectItem>
                    <SelectItem value="closed_won">Closed Won</SelectItem>
                    <SelectItem value="closed_lost">Closed Lost</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedOpportunities(new Set())
                    setShowBulkActions(false)
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}

        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading opportunities...</div>
          ) : filteredOpportunities.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No opportunities found. Create your first opportunity to get started.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedOpportunities.size === filteredOpportunities.length && filteredOpportunities.length > 0}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all opportunities"
                    />
                  </TableHead>
                  <SortableHeader field="title">Opportunity</SortableHeader>
                  <SortableHeader field="company">Company</SortableHeader>
                  <SortableHeader field="value">Value</SortableHeader>
                  <SortableHeader field="stage">Stage</SortableHeader>
                  <SortableHeader field="probability">Probability</SortableHeader>
                  <SortableHeader field="expected_close_date">Close Date</SortableHeader>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOpportunities.map((opportunity) => (
                  <TableRow key={opportunity.id} className={selectedOpportunities.has(opportunity.id) ? "bg-blue-50" : ""}>
                    <TableCell>
                      <Checkbox
                        checked={selectedOpportunities.has(opportunity.id)}
                        onCheckedChange={() => handleSelectOpportunity(opportunity.id)}
                        aria-label={`Select ${opportunity.title}`}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <Target className="h-4 w-4 text-purple-600" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {opportunity.title && opportunity.title.trim() !== ''
                              ? opportunity.title
                              : `${opportunity.company || 'Unknown Company'} Opportunity`
                            }
                          </div>
                          <div className="text-sm text-gray-500">{opportunity.contact_person || "—"}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Building2 className="h-4 w-4 mr-2 text-gray-400" />
                        {opportunity.company}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center font-medium">
                        <DollarSign className="h-4 w-4 mr-1 text-green-600" />
                        {opportunity.value.toLocaleString()}
                      </div>
                    </TableCell>
                    <TableCell>{getStageBadge(opportunity.stage)}</TableCell>
                    <TableCell>
                      <span className={`font-medium ${getProbabilityColor(opportunity.probability)}`}>
                        {opportunity.probability}%
                      </span>
                    </TableCell>
                    <TableCell>
                      {opportunity.expected_close_date ? (
                        <div className="flex items-center text-sm">
                          <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                          {new Date(opportunity.expected_close_date).toLocaleDateString()}
                        </div>
                      ) : (
                        "—"
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedOpportunity(opportunity)
                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setDeletingOpportunityId(opportunity.id)
                              setIsDeleteDialogOpen(true)
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Opportunity</DialogTitle>
            <DialogDescription>
              Update opportunity information and stage.
            </DialogDescription>
          </DialogHeader>
          {selectedOpportunity && (
            <OpportunityForm
              opportunity={selectedOpportunity}
              onSubmit={handleUpdateOpportunity}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setSelectedOpportunity(null)
              }}
              isEdit
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Opportunity</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this opportunity? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setIsDeleteDialogOpen(false)
              setDeletingOpportunityId(null)
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteOpportunity} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}